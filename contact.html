<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - JJ Forex</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" id="mainNav">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="jjraipurlogo.jpg" alt="JJ Forex" height="40" class="me-2">
                <span class="brand-text">JJ Forex</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-info-circle me-1"></i>About Forex
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="index.html#forex-intro">
                                <i class="fas fa-chart-line me-2"></i>What is Forex?
                            </a></li>
                            <li><a class="dropdown-item" href="index.html#forex-market">
                                <i class="fas fa-globe me-2"></i>Global Market
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="about.html">
                                <i class="fas fa-building me-2"></i>About JJ Forex
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cogs me-1"></i>Services
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="services.html">
                                <i class="fas fa-money-bill-transfer me-2"></i>Money Transfer
                            </a></li>
                            <li><a class="dropdown-item" href="services.html">
                                <i class="fas fa-paper-plane me-2"></i>Wire Transfer
                            </a></li>
                            <li><a class="dropdown-item" href="services.html">
                                <i class="fas fa-shield-alt me-2"></i>Travel Insurance
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="services.html">
                                <i class="fas fa-list me-2"></i>All Services
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="calculator.html">
                            <i class="fas fa-calculator me-1"></i>Rates
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.html">
                            <i class="fas fa-phone me-1"></i>Contact
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light btn-sm ms-2" href="https://wa.me/919827922102" target="_blank">
                            <i class="fab fa-whatsapp me-1"></i>Chat Now
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">Contact JJ Forex</h1>
                    <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
                        Ready to start your forex journey? Get in touch with our experts
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Methods -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="section-title">Get In Touch</h2>
                    <p class="section-subtitle">Multiple ways to reach our forex experts</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="contact-method-card-page">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h4>Call Us</h4>
                        <p>Speak directly with our forex experts for immediate assistance</p>
                        <div class="contact-details">
                            <a href="tel:+919827922102" class="contact-link">
                                <i class="fas fa-mobile-alt me-2"></i>+91-9827922102
                            </a>
                            <a href="tel:+919329503680" class="contact-link">
                                <i class="fas fa-mobile-alt me-2"></i>+91-9329503680
                            </a>
                            <a href="tel:07712543367" class="contact-link">
                                <i class="fas fa-phone me-2"></i>(0771) 2543367
                            </a>
                            <a href="tel:07712532455" class="contact-link">
                                <i class="fas fa-phone me-2"></i>(0771) 2532455
                            </a>
                        </div>
                        <div class="contact-hours">
                            <i class="fas fa-clock me-2"></i>
                            <span>Mon-Sat: 9:00 AM - 7:00 PM</span>
                        </div>
                        <a href="tel:+919827922102" class="btn btn-primary mt-3">
                            <i class="fas fa-phone me-2"></i>Call Now
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="contact-method-card-page featured">
                        <div class="featured-badge">Most Popular</div>
                        <div class="contact-icon">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <h4>WhatsApp</h4>
                        <p>Get instant support via WhatsApp chat for quick queries and assistance</p>
                        <div class="contact-details">
                            <span class="contact-info">
                                <i class="fab fa-whatsapp me-2"></i>+91-9827922102
                            </span>
                            <span class="contact-info">
                                <i class="fas fa-clock me-2"></i>Available 24/7
                            </span>
                            <span class="contact-info">
                                <i class="fas fa-language me-2"></i>Hindi & English
                            </span>
                            <span class="contact-info">
                                <i class="fas fa-reply me-2"></i>Quick Response
                            </span>
                        </div>
                        <a href="https://wa.me/919827922102" class="btn btn-success mt-3" target="_blank">
                            <i class="fab fa-whatsapp me-2"></i>Chat on WhatsApp
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="contact-method-card-page">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h4>Email Us</h4>
                        <p>Send us detailed queries via email for comprehensive responses</p>
                        <div class="contact-details">
                            <a href="mailto:<EMAIL>" class="contact-link">
                                <i class="fas fa-envelope me-2"></i><EMAIL>
                            </a>
                        </div>
                        <div class="contact-hours">
                            <i class="fas fa-reply me-2"></i>
                            <span>Response within 24 hours</span>
                        </div>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary mt-3">
                            <i class="fas fa-envelope me-2"></i>Send Email
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Office Location -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="section-title">Visit Our Office</h2>
                    <p class="section-subtitle">Meet us at our Raipur office for personalized service</p>
                </div>
            </div>
            
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right">
                    <div class="office-info">
                        <h3>JJ Forex Office</h3>
                        <div class="office-details">
                            <div class="office-detail">
                                <i class="fas fa-building"></i>
                                <div>
                                    <h5>Address</h5>
                                    <p>1st Floor, Fahd Complex<br>
                                    Saddani Chowk, Sadar Bazar<br>
                                    Raipur, Chhattisgarh - 492001</p>
                                </div>
                            </div>
                            <div class="office-detail">
                                <i class="fas fa-clock"></i>
                                <div>
                                    <h5>Business Hours</h5>
                                    <p>Monday - Saturday: 9:00 AM - 7:00 PM<br>
                                    Sunday: Closed<br>
                                    <small>Extended hours during peak seasons</small></p>
                                </div>
                            </div>
                            <div class="office-detail">
                                <i class="fas fa-car"></i>
                                <div>
                                    <h5>Parking</h5>
                                    <p>Free parking available<br>
                                    Easy access from main road</p>
                                </div>
                            </div>
                        </div>
                        <div class="office-actions">
                            <a href="#" class="btn btn-primary me-3" onclick="openMap()">
                                <i class="fas fa-directions me-2"></i>Get Directions
                            </a>
                            <a href="tel:+919827922102" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>Call Before Visit
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="map-container">
                        <div class="map-placeholder">
                            <i class="fas fa-map-marked-alt"></i>
                            <h4>Interactive Map</h4>
                            <p>Click "Get Directions" to open in Google Maps</p>
                            <button class="btn btn-outline-primary" onclick="openMap()">
                                <i class="fas fa-external-link-alt me-2"></i>Open in Google Maps
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="section-title">Send us a Message</h2>
                    <p class="section-subtitle">Fill out the form below and we'll get back to you within 24 hours</p>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-form-page" data-aos="fade-up">
                        <form id="contactForm" class="contact-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-group">
                                        <label><i class="fas fa-user me-2"></i>Full Name *</label>
                                        <input type="text" class="form-control" name="name" placeholder="Enter your full name" required>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-group">
                                        <label><i class="fas fa-envelope me-2"></i>Email Address *</label>
                                        <input type="email" class="form-control" name="email" placeholder="Enter your email" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-group">
                                        <label><i class="fas fa-phone me-2"></i>Phone Number *</label>
                                        <input type="tel" class="form-control" name="phone" placeholder="Enter your phone number" required>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-group">
                                        <label><i class="fas fa-cogs me-2"></i>Service Required *</label>
                                        <select class="form-select" name="service" required>
                                            <option value="">Select a service</option>
                                            <option value="moneygram">MoneyGram Transfer</option>
                                            <option value="western-union">Western Union</option>
                                            <option value="wire-transfer">Wire Transfer (SWIFT)</option>
                                            <option value="currency-exchange">Currency Exchange</option>
                                            <option value="travel-insurance">Travel Insurance</option>
                                            <option value="rate-inquiry">Rate Inquiry</option>
                                            <option value="general-inquiry">General Inquiry</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-group">
                                        <label><i class="fas fa-dollar-sign me-2"></i>Transfer Amount (Optional)</label>
                                        <input type="text" class="form-control" name="amount" placeholder="e.g., $1000 or ₹50000">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-group">
                                        <label><i class="fas fa-globe me-2"></i>Destination Country (Optional)</label>
                                        <input type="text" class="form-control" name="country" placeholder="e.g., USA, UK, Canada">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-group">
                                    <label><i class="fas fa-comment me-2"></i>Message *</label>
                                    <textarea class="form-control" name="message" rows="5" placeholder="Tell us about your forex requirements, questions, or any specific needs..." required></textarea>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </form>

                        <div class="contact-footer-info">
                            <div class="row text-center mt-4">
                                <div class="col-md-4">
                                    <div class="contact-stat">
                                        <i class="fas fa-clock"></i>
                                        <span>24 Hour Response</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="contact-stat">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>RBI Licensed</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="contact-stat">
                                        <i class="fas fa-users"></i>
                                        <span>Expert Support</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="section-title">Frequently Asked Questions</h2>
                    <p class="section-subtitle">Quick answers to common forex queries</p>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item" data-aos="fade-up" data-aos-delay="100">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    What documents are required for money transfer?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    For money transfers, you need: Valid passport, PAN card, Aadhaar card, and purpose documentation (invoice, admission letter, etc.). Additional documents may be required based on the transfer amount and purpose.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item" data-aos="fade-up" data-aos-delay="200">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    How long does a wire transfer take?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Wire transfers typically take 1-3 business days depending on the destination country and bank. Same-day processing is available for complete applications submitted before 2 PM.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item" data-aos="fade-up" data-aos-delay="300">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    What are your exchange rates?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Our exchange rates are competitive and updated in real-time based on market conditions. Rates may vary based on transfer amount and method. Contact us for current rates or use our online calculator.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item" data-aos="fade-up" data-aos-delay="400">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    Is travel insurance mandatory?
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Travel insurance is mandatory for Schengen visa applications and highly recommended for all international travel. It covers medical emergencies, trip cancellations, and other unforeseen circumstances.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-enhanced">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                        <img src="jjraipurlogo.jpg" alt="JJ Forex" height="50" class="mb-3">

                    <div class="footer-brand">
                        <h4>JJ Forex</h4>
                        <p>Your trusted partner in foreign exchange services since 2005. RBI FFMC Licensed with 19+ years of excellence in international money transfers.</p>
                        <div class="footer-badges">
                            <div class="badge-item">
                                <i class="fas fa-certificate"></i>
                                <span>RBI Licensed</span>
                            </div>
                            <div class="badge-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Secure Transfers</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-2 mb-4">
                    <h6>Quick Links</h6>
                    <ul class="footer-links">
                        <li><a href="index.html"><i class="fas fa-home me-2"></i>Home</a></li>
                        <li><a href="index.html#forex-intro"><i class="fas fa-chart-line me-2"></i>About Forex</a></li>
                        <li><a href="services.html"><i class="fas fa-cogs me-2"></i>Services</a></li>
                        <li><a href="calculator.html"><i class="fas fa-calculator me-2"></i>Rates</a></li>
                        <li><a href="contact.html"><i class="fas fa-phone me-2"></i>Contact</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h6>Forex Services</h6>
                    <ul class="footer-links">
                        <li><a href="services.html"><i class="fas fa-money-bill-transfer me-2"></i>MoneyGram</a></li>
                        <li><a href="services.html"><i class="fas fa-globe-americas me-2"></i>Western Union</a></li>
                        <li><a href="services.html"><i class="fas fa-university me-2"></i>Wire Transfer</a></li>
                        <li><a href="services.html"><i class="fas fa-shield-alt me-2"></i>Travel Insurance</a></li>
                        <li><a href="calculator.html"><i class="fas fa-exchange-alt me-2"></i>Currency Exchange</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h6>Get In Touch</h6>
                    <div class="footer-contact">
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <strong>Office Address</strong>
                                <p>1st Floor, Fahd Complex<br>Saddani Chowk, Sadar Bazar<br>Raipur, Chhattisgarh</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <div>
                                <strong>Phone Numbers</strong>
                                <p><a href="tel:+919827922102">+91-9827922102</a><br>
                                <a href="tel:07712543367">(0771) 2543367</a></p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <div>
                                <strong>Email</strong>
                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>
                    </div>

                    <div class="footer-actions">
                        <a href="https://wa.me/919827922102" class="btn btn-success btn-sm me-2" target="_blank">
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp
                        </a>
                        <button class="btn btn-outline-light btn-sm" data-bs-toggle="modal" data-bs-target="#quickInquiryModal">
                            <i class="fas fa-paper-plane me-1"></i>Quick Inquiry
                        </button>
                    </div>
                </div>
            </div>

            <hr class="footer-divider">

            <div class="row align-items-center">
                <div class="col-lg-6">
                    <p class="footer-copyright mb-0">
                        &copy; 2024 JJ Forex. All rights reserved. | RBI FFMC Licensed | 19+ Years of Excellence
                    </p>
                </div>
                <div class="col-lg-6 text-end">
                    <div class="social-links">
                        <a href="#" class="social-link" title="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link" title="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link" title="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Quick Inquiry Modal -->
    <div class="modal fade" id="quickInquiryModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Quick Inquiry</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="quickInquiryForm">
                        <div class="mb-3">
                            <input type="text" class="form-control" name="name" placeholder="Your Name" required>
                        </div>
                        <div class="mb-3">
                            <input type="tel" class="form-control" name="phone" placeholder="Your Phone" required>
                        </div>
                        <div class="mb-3">
                            <select class="form-select" name="service" required>
                                <option value="">Select Service</option>
                                <option value="forex-rates">Current Forex Rates</option>
                                <option value="money-transfer">Money Transfer</option>
                                <option value="travel-insurance">Travel Insurance</option>
                                <option value="other">Other Inquiry</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane me-2"></i>Send Inquiry
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.me/919827922102" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="js/script.js"></script>
    <script>
        // Google Maps integration
        function openMap() {
            const address = "1st Floor, Fahd Complex, Saddani Chowk, Sadar Bazar, Raipur, Chhattisgarh";
            const encodedAddress = encodeURIComponent(address);
            window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`, '_blank');
        }
    </script>
</body>
</html>

/* Custom CSS for JJ Forex */

:root {
    --primary-color: #1e3c72;
    --secondary-color: #2a5298;
    --accent-color: #00d4ff;
    --gradient-primary: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    --gradient-accent: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --text-dark: #2c3e50;
    --text-light: #ffffff;
    --bg-light: #f8f9fa;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Navigation Styles */
.navbar {
    background: rgba(30, 60, 114, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar.scrolled {
    background: rgba(30, 60, 114, 0.98);
    padding: 0.5rem 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
}

.brand-text {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background: var(--accent-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

/* Hero Slider */
.hero-slider {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
}

.hero-slide {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.hero-slide-1 {
    background: var(--gradient-primary);
}

.hero-slide-2 {
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
}

.hero-slide-3 {
    background: linear-gradient(135deg, #0099cc 0%, #00d4ff 100%);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.3;
}

/* Hero Content */
.hero-content {
    z-index: 2;
    position: relative;
}

/* Hero Stats Card */
.hero-stats-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 2rem;
    box-shadow: var(--shadow-hover);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.stats-grid .stat-item {
    text-align: center;
    color: var(--text-light);
}

.stats-grid .stat-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.2rem;
    color: white;
}

.stats-grid .stat-item h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stats-grid .stat-item p {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
}

/* Service Highlights */
.service-highlights {
    margin: 2rem 0;
}

.highlight-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--text-light);
    font-size: 1.1rem;
}

.highlight-item i {
    color: var(--accent-color);
    margin-right: 1rem;
    font-size: 1.3rem;
    min-width: 30px;
}

/* Services Preview in Hero */
.services-preview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
}

.service-card-mini {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    color: var(--text-light);
    transition: all 0.3s ease;
    animation: float 6s ease-in-out infinite;
}

.service-card-mini:nth-child(1) { animation-delay: 0s; }
.service-card-mini:nth-child(2) { animation-delay: 1.5s; }
.service-card-mini:nth-child(3) { animation-delay: 3s; }
.service-card-mini:nth-child(4) { animation-delay: 4.5s; }

.service-card-mini:hover {
    transform: translateY(-5px) scale(1.05);
    background: rgba(255, 255, 255, 0.15);
}

.service-card-mini i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.service-card-mini h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

/* Contact Options */
.contact-options {
    margin-top: 2rem;
}

.contact-options .btn {
    margin-bottom: 1rem;
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.8;
    transition: all 0.3s ease;
}

.carousel-control-prev {
    left: 30px;
}

.carousel-control-next {
    right: 30px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.5);
}

.carousel-indicators {
    bottom: 30px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background: rgba(255, 255, 255, 0.5);
    border: none;
    transition: all 0.3s ease;
}

.carousel-indicators button.active {
    background: var(--accent-color);
    transform: scale(1.2);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.text-gradient {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    color: var(--text-light);
}

.stat-item h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-buttons {
    margin-top: 2rem;
}

.btn-primary {
    background: var(--gradient-accent);
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

/* Hero Image */
.hero-image {
    position: relative;
    height: 500px;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    text-align: center;
    color: var(--text-light);
    animation: float 6s ease-in-out infinite;
    box-shadow: var(--shadow);
}

.floating-card:nth-child(1) {
    top: 50px;
    right: 100px;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    top: 200px;
    left: 50px;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    bottom: 100px;
    right: 50px;
    animation-delay: 4s;
}

.floating-card i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.floating-card h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.floating-card p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid var(--accent-color);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-accent);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 3rem;
}

/* Forex Content */
.forex-content h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.forex-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.feature-list {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature-item i {
    color: var(--accent-color);
    margin-right: 1rem;
    font-size: 1.2rem;
}

/* Forex Visual */
.forex-visual {
    text-align: center;
    padding: 2rem;
}

.currency-pair {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.currency {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    min-width: 120px;
}

.currency .flag {
    font-size: 2rem;
    display: block;
    margin-bottom: 0.5rem;
}

.currency .code {
    font-size: 1.5rem;
    font-weight: 700;
}

.exchange-arrow {
    margin: 0 2rem;
    font-size: 2rem;
    color: var(--accent-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.rate-display {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
}

.rate-display .rate {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.rate-display .pair {
    font-size: 1.1rem;
    color: #6c757d;
}

/* Company Timeline */
.company-timeline {
    position: relative;
    padding: 2rem 0;
}

.company-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-accent);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item:nth-child(odd) {
    flex-direction: row-reverse;
}

.timeline-year {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 1rem 1.5rem;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    min-width: 120px;
    text-align: center;
    box-shadow: var(--shadow);
    position: relative;
    z-index: 2;
}

.timeline-content {
    flex: 1;
    padding: 0 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow);
    padding: 2rem;
    margin: 0 2rem;
}

.timeline-content h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.timeline-content p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.service-card h4 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-card p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Market Features */
.market-info h3 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.market-info p {
    color: #6c757d;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.feature-box {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feature-box:hover {
    background: white;
    box-shadow: var(--shadow);
}

.feature-box i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-right: 1rem;
    min-width: 60px;
}

.feature-box h5 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.feature-box p {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

/* World Clock */
.world-clock {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    padding: 2rem;
    background: var(--gradient-primary);
    border-radius: 20px;
    color: var(--text-light);
}

.clock-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.clock-item .city {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.clock-item .time {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
}

/* Market Tiers */
.market-tiers {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
}

.tier-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    flex: 1;
    max-width: 300px;
}

.tier-card h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.tier-card p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

.tier-arrow {
    font-size: 2rem;
    color: var(--accent-color);
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .floating-card {
        position: static;
        margin-bottom: 1rem;
        animation: none;
    }

    .currency-pair {
        flex-direction: column;
        gap: 1rem;
    }

    .exchange-arrow {
        transform: rotate(90deg);
        margin: 1rem 0;
    }

    .company-timeline::before {
        left: 30px;
    }

    .timeline-item {
        flex-direction: row !important;
        padding-left: 60px;
    }

    .timeline-year {
        position: absolute;
        left: 0;
        min-width: 80px;
        font-size: 0.9rem;
        padding: 0.5rem;
    }

    .timeline-content {
        margin: 0;
        margin-left: 1rem;
    }

    .world-clock {
        grid-template-columns: 1fr;
    }

    .market-tiers {
        flex-direction: column;
    }

    .tier-arrow {
        transform: rotate(90deg);
    }
}

/* Live Currency Rates Ticker */
.rates-ticker {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
}

.ticker-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.rate-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-light);
    min-width: 120px;
}

.currency-pair {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 0.25rem;
}

.rate {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
}

.change.positive {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.change.negative {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.rates-info {
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 0.5rem;
}

/* Quick Stats Cards */
.stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-accent);
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.stat-card .stat-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: white;
}

.stat-card h3 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-card p {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.stat-card small {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Service Preview Cards */
.service-preview-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.service-preview-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.service-preview-card .service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all 0.3s ease;
}

.service-preview-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-preview-card h4 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.service-preview-card p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-preview-card .btn {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-preview-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Service Detail Cards */
.service-detail-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.service-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.service-icon-large {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
    color: white;
}

.service-header h3 {
    color: var(--text-dark);
    font-weight: 600;
    margin: 0;
}

.service-content p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features li {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.service-features li i {
    color: var(--accent-color);
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

/* Currency Converter */
.converter-card {
    background: white;
    border-radius: 25px;
    padding: 3rem;
    box-shadow: var(--shadow-hover);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.currency-input {
    margin-bottom: 1rem;
}

.currency-input label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.currency-input .form-control,
.currency-input .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.currency-input .form-control:focus,
.currency-input .form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
}

.swap-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding-top: 2rem;
}

.swap-button .btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    transition: all 0.3s ease;
}

.swap-button .btn:hover {
    background: var(--accent-color);
    color: white;
    transform: rotate(180deg);
}

.exchange-rate-display {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    border: 2px solid #e9ecef;
}

.exchange-rate-display.active {
    border-color: var(--accent-color);
    background: rgba(0, 212, 255, 0.05);
}

/* Contact Cards */
.contact-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.contact-card h4 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.contact-info p {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    color: #6c757d;
    text-align: left;
}

.contact-info p i {
    color: var(--accent-color);
    margin-right: 0.5rem;
    margin-top: 0.2rem;
    min-width: 20px;
}

.contact-form-card {
    background: var(--gradient-primary);
    color: var(--text-light);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    height: 100%;
}

.contact-form-card h4 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.contact-form-card .form-control,
.contact-form-card .form-select {
    border: none;
    border-radius: 10px;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-dark);
}

.contact-form-card .form-control:focus,
.contact-form-card .form-select:focus {
    background: white;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* Footer */
.footer {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-brand img {
    filter: brightness(0) invert(1);
}

.footer-brand h5 {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer h6 {
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-color);
    padding-left: 5px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 2rem 0 1rem;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* Floating WhatsApp Button */
.whatsapp-float {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    text-decoration: none;
    box-shadow: var(--shadow-hover);
    z-index: 1000;
    transition: all 0.3s ease;
    animation: pulse-whatsapp 2s infinite;
}

.whatsapp-float:hover {
    background: #128c7e;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(37, 211, 102, 0.4);
}

@keyframes pulse-whatsapp {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Trust Elements */
.trust-badges {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    color: var(--text-light);
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.trust-badge i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .services-preview {
        grid-template-columns: 1fr;
    }

    .ticker-content {
        flex-direction: column;
        gap: 0.5rem;
    }

    .rate-item {
        min-width: auto;
        flex-direction: row;
        gap: 1rem;
    }

    .whatsapp-float {
        bottom: 80px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 40px;
        height: 40px;
    }

    .carousel-control-prev {
        left: 15px;
    }

    .carousel-control-next {
        right: 15px;
    }

    .service-highlights {
        margin: 1rem 0;
    }

    .highlight-item {
        font-size: 1rem;
    }

    .contact-options .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .stats-grid .stat-item h3 {
        font-size: 1.5rem;
    }

    .trust-badges {
        flex-direction: column;
        gap: 1rem;
    }

    .trust-badge {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

/* Notification Toast */
.notification-toast {
    position: fixed;
    top: 100px;
    right: 30px;
    z-index: 9999;
    min-width: 300px;
    animation: slideInRight 0.3s ease;
    box-shadow: var(--shadow-hover);
    border: none;
    border-radius: 10px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced Button Styles */
.btn-success {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: linear-gradient(135deg, #128c7e 0%, #25d366 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
}

/* Loading States */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

/* Enhanced Modal Styles */
.modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: var(--shadow-hover);
}

.modal-header {
    background: var(--gradient-primary);
    color: var(--text-light);
    border-radius: 20px 20px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 2rem;
}

/* Currency Converter Enhancements */
.converter-card {
    position: relative;
    overflow: hidden;
}

.converter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-accent);
}

/* Service Cards Hover Effects */
.service-preview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.service-preview-card:hover::before {
    left: 100%;
}

/* Enhanced Typography */
.hero-title {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.section-title {
    position: relative;
    display: inline-block;
}

.section-title::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    z-index: -1;
    border-radius: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.section-title:hover::before {
    opacity: 1;
}

/* Accessibility Improvements */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Print Optimizations */
@media print {
    .whatsapp-float,
    .back-to-top,
    .notification-toast,
    .carousel-control-prev,
    .carousel-control-next,
    .carousel-indicators {
        display: none !important;
    }

    .hero-slide {
        background: white !important;
        color: black !important;
    }

    .hero-overlay {
        display: none !important;
    }
}

/* About Page Styles */
.page-header {
    background: var(--gradient-primary);
    padding: 120px 0 80px;
    color: var(--text-light);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

/* Company Story */
.company-story h3 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.company-story p {
    color: #6c757d;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.highlight-box {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    margin-top: 2rem;
    position: relative;
}

.highlight-box i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.highlight-box .quote {
    font-size: 1.3rem;
    font-style: italic;
    margin-bottom: 1rem;
}

.highlight-box small {
    opacity: 0.8;
}

/* Timeline Visual */
.timeline-visual {
    position: relative;
    padding: 2rem 0;
}

.timeline-visual::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-accent);
}

.timeline-visual .timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    position: relative;
}

.timeline-visual .timeline-year {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 700;
    min-width: 80px;
    text-align: center;
    box-shadow: var(--shadow);
    position: relative;
    z-index: 2;
}

.timeline-visual .timeline-content {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 1.5rem;
    margin-left: 2rem;
    flex: 1;
}

.timeline-visual .timeline-content h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-visual .timeline-content p {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

/* Operation Cards */
.operation-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.operation-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.operation-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.operation-card h4 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.operation-card ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.operation-card li {
    color: #6c757d;
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.operation-card li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
}

/* Forex Education */
.forex-education h3 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.forex-education p {
    color: #6c757d;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.forex-benefits h4 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.benefit-list {
    list-style: none;
    padding: 0;
}

.benefit-list li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: #6c757d;
}

.benefit-list li i {
    color: var(--accent-color);
    margin-right: 1rem;
    font-size: 1.2rem;
    min-width: 20px;
}

/* Market Stats */
.market-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.stat-box {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.stat-box h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-box p {
    color: #6c757d;
    margin: 0;
    font-weight: 600;
}

/* Structure Cards */
.structure-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
}

.structure-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.structure-card h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.structure-card h4 i {
    color: var(--accent-color);
}

.structure-card p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* CTA Section */
.cta-section {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="300" cy="300" r="150" fill="url(%23a)"/><circle cx="700" cy="200" r="100" fill="url(%23a)"/></svg>');
    opacity: 0.3;
}

.cta-buttons {
    position: relative;
    z-index: 2;
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .timeline-visual::before {
        left: 15px;
    }

    .timeline-visual .timeline-year {
        min-width: 60px;
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .timeline-visual .timeline-content {
        margin-left: 1rem;
    }

    .market-stats {
        grid-template-columns: 1fr;
    }

    .stat-box h3 {
        font-size: 2rem;
    }
}

/* Button Animations */
.btn {
    position: relative;
    overflow: hidden;
}

.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Additional Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hover Effects */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 60, 114, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-accent);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Print Styles */
@media print {
    .navbar,
    .back-to-top,
    .floating-card {
        display: none !important;
    }

    .hero-section {
        background: white !important;
        color: black !important;
    }

    .section-title {
        color: black !important;
    }
}

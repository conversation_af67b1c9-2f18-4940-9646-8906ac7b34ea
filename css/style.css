/* Custom CSS for JJ Forex */

:root {
    --primary-color: #1e3c72;
    --secondary-color: #2a5298;
    --accent-color: #00d4ff;
    --gradient-primary: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    --gradient-accent: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --text-dark: #2c3e50;
    --text-light: #ffffff;
    --bg-light: #f8f9fa;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Navigation Styles */
.navbar {
    background: rgba(30, 60, 114, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar.scrolled {
    background: rgba(30, 60, 114, 0.98);
    padding: 0.5rem 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
}

.brand-text {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background: var(--accent-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.3;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.text-gradient {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    color: var(--text-light);
}

.stat-item h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-buttons {
    margin-top: 2rem;
}

.btn-primary {
    background: var(--gradient-accent);
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

/* Hero Image */
.hero-image {
    position: relative;
    height: 500px;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    text-align: center;
    color: var(--text-light);
    animation: float 6s ease-in-out infinite;
    box-shadow: var(--shadow);
}

.floating-card:nth-child(1) {
    top: 50px;
    right: 100px;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    top: 200px;
    left: 50px;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    bottom: 100px;
    right: 50px;
    animation-delay: 4s;
}

.floating-card i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.floating-card h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.floating-card p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid var(--accent-color);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-accent);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 3rem;
}

/* Forex Content */
.forex-content h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.forex-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.feature-list {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature-item i {
    color: var(--accent-color);
    margin-right: 1rem;
    font-size: 1.2rem;
}

/* Forex Visual */
.forex-visual {
    text-align: center;
    padding: 2rem;
}

.currency-pair {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.currency {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    min-width: 120px;
}

.currency .flag {
    font-size: 2rem;
    display: block;
    margin-bottom: 0.5rem;
}

.currency .code {
    font-size: 1.5rem;
    font-weight: 700;
}

.exchange-arrow {
    margin: 0 2rem;
    font-size: 2rem;
    color: var(--accent-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.rate-display {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
}

.rate-display .rate {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.rate-display .pair {
    font-size: 1.1rem;
    color: #6c757d;
}

/* Company Timeline */
.company-timeline {
    position: relative;
    padding: 2rem 0;
}

.company-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-accent);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item:nth-child(odd) {
    flex-direction: row-reverse;
}

.timeline-year {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 1rem 1.5rem;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    min-width: 120px;
    text-align: center;
    box-shadow: var(--shadow);
    position: relative;
    z-index: 2;
}

.timeline-content {
    flex: 1;
    padding: 0 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow);
    padding: 2rem;
    margin: 0 2rem;
}

.timeline-content h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.timeline-content p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.service-card h4 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-card p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Market Features */
.market-info h3 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.market-info p {
    color: #6c757d;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.feature-box {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feature-box:hover {
    background: white;
    box-shadow: var(--shadow);
}

.feature-box i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-right: 1rem;
    min-width: 60px;
}

.feature-box h5 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.feature-box p {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

/* World Clock */
.world-clock {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    padding: 2rem;
    background: var(--gradient-primary);
    border-radius: 20px;
    color: var(--text-light);
}

.clock-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.clock-item .city {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.clock-item .time {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
}

/* Market Tiers */
.market-tiers {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
}

.tier-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    flex: 1;
    max-width: 300px;
}

.tier-card h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.tier-card p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

.tier-arrow {
    font-size: 2rem;
    color: var(--accent-color);
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .floating-card {
        position: static;
        margin-bottom: 1rem;
        animation: none;
    }

    .currency-pair {
        flex-direction: column;
        gap: 1rem;
    }

    .exchange-arrow {
        transform: rotate(90deg);
        margin: 1rem 0;
    }

    .company-timeline::before {
        left: 30px;
    }

    .timeline-item {
        flex-direction: row !important;
        padding-left: 60px;
    }

    .timeline-year {
        position: absolute;
        left: 0;
        min-width: 80px;
        font-size: 0.9rem;
        padding: 0.5rem;
    }

    .timeline-content {
        margin: 0;
        margin-left: 1rem;
    }

    .world-clock {
        grid-template-columns: 1fr;
    }

    .market-tiers {
        flex-direction: column;
    }

    .tier-arrow {
        transform: rotate(90deg);
    }
}

/* Service Detail Cards */
.service-detail-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.service-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.service-icon-large {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
    color: white;
}

.service-header h3 {
    color: var(--text-dark);
    font-weight: 600;
    margin: 0;
}

.service-content p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features li {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.service-features li i {
    color: var(--accent-color);
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

/* Currency Converter */
.converter-card {
    background: white;
    border-radius: 25px;
    padding: 3rem;
    box-shadow: var(--shadow-hover);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.currency-input {
    margin-bottom: 1rem;
}

.currency-input label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.currency-input .form-control,
.currency-input .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.currency-input .form-control:focus,
.currency-input .form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
}

.swap-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding-top: 2rem;
}

.swap-button .btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    transition: all 0.3s ease;
}

.swap-button .btn:hover {
    background: var(--accent-color);
    color: white;
    transform: rotate(180deg);
}

.exchange-rate-display {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    border: 2px solid #e9ecef;
}

.exchange-rate-display.active {
    border-color: var(--accent-color);
    background: rgba(0, 212, 255, 0.05);
}

/* Contact Cards */
.contact-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.contact-card h4 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.contact-info p {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    color: #6c757d;
    text-align: left;
}

.contact-info p i {
    color: var(--accent-color);
    margin-right: 0.5rem;
    margin-top: 0.2rem;
    min-width: 20px;
}

.contact-form-card {
    background: var(--gradient-primary);
    color: var(--text-light);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    height: 100%;
}

.contact-form-card h4 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.contact-form-card .form-control,
.contact-form-card .form-select {
    border: none;
    border-radius: 10px;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-dark);
}

.contact-form-card .form-control:focus,
.contact-form-card .form-select:focus {
    background: white;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* Footer */
.footer {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-brand img {
    filter: brightness(0) invert(1);
}

.footer-brand h5 {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer h6 {
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-color);
    padding-left: 5px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 2rem 0 1rem;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* Button Animations */
.btn {
    position: relative;
    overflow: hidden;
}

.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Additional Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hover Effects */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 60, 114, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-accent);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Print Styles */
@media print {
    .navbar,
    .back-to-top,
    .floating-card {
        display: none !important;
    }

    .hero-section {
        background: white !important;
        color: black !important;
    }

    .section-title {
        color: black !important;
    }
}
